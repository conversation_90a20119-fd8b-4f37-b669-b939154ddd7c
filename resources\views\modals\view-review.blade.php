<div class="main_cart d-flex justify-content-between p-2 review_main_card">
    <input type="hidden" name="booking_ids" value="{{ $review->booking->ids ?? '' }}">
    <div class="property-image">
        <img class="" src="{{ asset('website') . '/' . ($review->listing->thumbnail_image->url ?? '') }}"
            loading="lazy" width="250"
            onerror="this.onerror=null;this.src=`{{ asset('website/images/plcaeholderListingImg.png') }}`;">
    </div>
    <div class="cart-asset d-flex flex-wrap w-75 pe-3 review_wrapper">
        <div class="d-flex justify-content-between align-items-center flex-wrap w-100">
            <h4 class="fs-26 light_bold list_product_heading ">
                {{ $review->listing->name ?? 'Listing Name Here' }}</h4>
            <p class="rating  m-0 ">
                <i class="fa fa-star"></i>
                {{ $review->listing->rating == 0 ? translate('user_review_popup.new_listing') : number_format($review->listing->rating, 1) }}
            </p>
        </div>
        <div class="service_provider">
            <div class="d-flex gap-2 align-items-center pt-2">
                <div class="user_img">
                    <img class="img-fluid" src="{{ asset('website') . '/' . ($review->provider->avatar ?? '') }}"
                        onerror="this.onerror=null;this.src=`{{ asset('website/images/plcaeholderListingImg.png') }}`;"
                        alt="profile image">
                </div>
                <div class="provider-content">
                    <div class="provider-name">
                        {{ $review->listing->user->first_name ?? 'John' }}
                        {{ $review->listing->user->last_name ?? 'Doe' }}
                    </div>
                    <div class="provider-rating">
                        <p class="rating fs-12 text-black-50">
                            <i class="fa fa-star"></i>
                            @if (count($review->listing->user->provider_review ?? []) < 5)
                                {{-- New Host --}}
                                {{ translate('user_review_popup.new_host') }}
                            @else
                                {{ count($review->listing->user->provider_review ?? []) }} {{ __('website.reviews') }}
                            @endif
                        </p>
                    </div>
                </div>
            </div>
            <div class="d-flex gap-5 alignitems-center pt-3 dates-wrapper">

                @php
                    $dateLabels = [
                        1 => ['Start & End Date', translate('user_review_popup.start_date'), translate('user_review_popup.end_date')],
                        2 => ['Departure & Return Date', translate('user_review_popup.departure_date'), translate('user_review_popup.return_date')],
                        3 => ['Pick Up & Drop Off Date', translate('user_review_popup.pick_up_time'), translate('user_review_popup.drop_off_time')],
                        4 => ['Check-in & Check-out Date', translate('user_review_popup.checkin_date'), translate('user_review_popup.checkout_date')],
                    ];
                    $isSingleDay = $review->booking->detail->total_days < 1;
                    $startLabel = $isSingleDay
                        ? $dateLabels[$review->listing->category_id][1]
                        : $dateLabels[$review->listing->category_id][1];
                    $endLabel = $isSingleDay ? null : $dateLabels[$review->listing->category_id][2];
                @endphp

                <div class="start">
                    <p class="light-bold m-0 fs-14">{{ $startLabel }}</p>
                    <p class="fs-14 start-date">
                        {{ translateDynamic(date(config('constant.date_format'), strtotime($review->booking->check_in ?? '-'))) }}
                    </p>
                </div>
                <div class="end">
                    <p class="light-bold m-0 fs-14">{{ $endLabel }}</p>
                    <p class="fs-14 end-date">
                        {{ translateDynamic(date(config('constant.date_format'), strtotime($review->booking->check_out ?? '-'))) }}
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>
<form action="" class="rating leave_review pt-4 w-100">
    @php
        $rating_comment = array_reverse([translate('user_review_popup.poor'), translate('user_review_popup.fair'), translate('user_review_popup.good'), translate('user_review_popup.very_good'), translate('user_review_popup.excellent')]);
        $rating_numbers = array_reverse([1, 2, 3, 4, 5]);
    @endphp
    <div class="rating-options">
        <!-- Cleanliness Rating -->
        <div class="rating-option-single d-flex justify-content-between align-items-center py-2 cleanliness">
            <div class="rating_title">{{ translate('user_bookings.cleanliness') }}</div>
            <div class="rating_star_wrapper">
                @for ($r = 0; $r < count($rating_numbers); $r++)
                    <input type="checkbox" class="rating-checkbox d-none" value="{{ $rating_numbers[$r] }}"
                        name="cleanliness" id="rating-clean-1-{{ $r }}">
                    <label for="rating-clean-1-{{ $r }}" tooltip="{{ $rating_comment[$r] }}">
                        <i class="fa fa-star {{ $rating_numbers[$r] <= $review->cleanliness ? 'dark-yellow' : '' }}"></i>
                    </label>
                @endfor
            </div>
        </div>
        <!-- Communication Rating -->
        <div class="rating-option-single d-flex justify-content-between align-items-center py-2 communication">
            <div class="rating_title">{{ translate('user_bookings.communication') }}</div>
            <div class="rating_star_wrapper">
                @for ($c = 0; $c < count($rating_numbers); $c++)
                    <input type="checkbox" class="rating-checkbox d-none" value="{{ $rating_numbers[$c] }}"
                        name="communication" id="rating-comm-1-{{ $c }}">
                    <label for="rating-comm-1-{{ $c }}" tooltip="{{ $rating_comment[$c] }}">
                        <i class="fas fa-star {{ $rating_numbers[$c] <= $review->communication ? 'dark-yellow' : '' }}"></i>
                    </label>
                @endfor
            </div>
        </div>
        <!-- Amenities/Features Rating -->
        <div class="rating-option-single d-flex justify-content-between align-items-center py-2 features">
            <div class="rating_title">{{ translate('user_bookings.amenities_features') }}</div>
            <div class="rating_star_wrapper">
                @for ($a = 0; $a < count($rating_numbers); $a++)
                    <input type="checkbox" class="rating-checkbox d-none" value="{{ $rating_numbers[$a] }}"
                        name="features" id="rating-features-1-{{ $a }}">
                    <label for="rating-features-1-{{ $a }}" tooltip="{{ $rating_comment[$a] }}">
                        <i class="fas fa-star {{ $rating_numbers[$a] <= $review->features ? 'dark-yellow' : '' }}"></i>
                    </label>
                @endfor
            </div>
        </div>
        <!-- Value for Money Rating -->
        <div class="rating-option-single d-flex justify-content-between align-items-center py-2 value_money">
            <div class="rating_title">{{ translate('user_bookings.value_for_money') }}</div>
            <div class="rating_star_wrapper">
                @for ($m = 0; $m < count($rating_numbers); $m++)
                    <input type="checkbox" class="rating-checkbox d-none" value="{{ $rating_numbers[$m] }}"
                        name="value_of_money" id="rating-money-1-{{ $m }}">
                    <label for="rating-money-1-{{ $m }}" tooltip="{{ $rating_comment[$m] }}">
                        <i class="fas fa-star {{ $rating_numbers[$m] <= $review->value_of_money ? 'dark-yellow' : '' }}"></i>
                    </label>
                @endfor
            </div>
        </div>
        <div class="rating-option-single d-flex justify-content-between align-items-center py-2 value_money">
            <div class="rating_title">{{ translate('user_bookings.accuracy_of_description') }}</div>
            <div class="rating_star_wrapper">
                @for ($s = 0; $s < count($rating_numbers); $s++)
                    <input type="checkbox" class="rating-checkbox d-none" value="{{ $rating_numbers[$s] }}"
                        {{-- Add a condition in above line similar to the above option replacing variable name after adding column in database @Zohaib @Ushba --}} name="accuracy_of_description"
                        id="rating-accuracy-description-1-{{ $s }}">
                    <label for="rating-accuracy-description-1-{{ $s }}"
                        tooltip="{{ $rating_comment[$s] }}">
                        <i
                            class="fas fa-star {{ $rating_numbers[$s] <= $review->accuracy_of_description ? 'dark-yellow' : '' }}"></i>
                    </label>
                @endfor
            </div>
        </div>
        <!-- Overall Experience Rating -->
        <div class="rating-option-single d-flex justify-content-between align-items-center py-2 overall_exp">
            <div class="bold rating_title">{{ translate('user_bookings.overall_experience') }}</div>
            <div class="rating_star_wrapper">
                @for ($o = 0; $o < count($rating_numbers); $o++)
                    <input type="checkbox" class="rating-checkbox d-none" value="{{ $rating_numbers[$o] }}"
                        name="rating" id="rating-overall-1-{{ $o }}">
                    <label for="rating-overall-1-{{ $o }}" tooltip="{{ $rating_comment[$o] }}">
                        <i class="fas fa-star {{ $rating_numbers[$o] <= $review->rating ? 'dark-yellow' : '' }}"></i>
                    </label>
                @endfor
            </div>
        </div>
    </div>
    <div class="images_wrapper my-3">
        @if ($review->images)
            @foreach ($review->images as $image)
                <div class="image_wrapper">
                    <a href="{{ asset('website') . '/' . $image->image }}" data-fancybox="review-gallery">
                        <img src="{{ asset('website') . '/' . $image->image }}" width="100" height="100"
                            alt="review image">
                    </a>
                </div>
            @endforeach
        @endif
    </div>
    @if($review->comment)
    <hr>
        <div class="review_comment ">
            <h6>{{ translate('user_review_popup.comment') }}</h6>
            {!! translateDynamic($review->comment) !!}
        </div>
    @endif

    @if ($review->reply)
        <div class="review_comment ">
            <h6>{{ translate('user_review_popup.service_provider_response') }}</h6>
            {!! translateDynamic($review->reply->reply) !!}
        </div>
    @endif
</form>

@if (isset($reply_mode) && $reply_mode && auth()->user()->hasRole('service') && ($review->reply->is_updated ?? null) != 1)
    <hr>
    <form action="{{ route('review.reply', $review->id) }}" method="POST" class="reply_form">
        @csrf
        <div class="review_comment mb-3">
            <h6>{{ translate('user_review_popup.write_your_reply') }}</h6>
            <textarea name="reply" id="reply_textarea_{{ $review->id }}" cols="30" rows="5" class="form-control"
                placeholder="{{ translate('user_review_popup.write_your_response_here') }}">{{ $review->reply->reply ?? "" }}</textarea>
        </div>
        <div class="d-flex gap-2">
            <button type="submit" class="btn_yellow">{{ translate('user_review_popup.post_reply') }}</button>
            <button type="button" class="btn_black" data-dismiss="modal">{{ translate('user_review_popup.cancel') }}</button>
        </div>
    </form>
@endif
