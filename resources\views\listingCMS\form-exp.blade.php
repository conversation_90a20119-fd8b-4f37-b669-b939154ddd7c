@push('css')
    <link href="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.4/css/select2.min.css" rel="stylesheet" />
    <link rel="stylesheet" href="{{ asset('plugins/components/dropify/dist/css/dropify.min.css') }}">
@endpush

<div class="steps_parent">
    <div id="accordion">
        <div class="accordion-item">
            <div class="card-header" id="headingOne">
                <h5 class="m-0">
                    <a class="btn btn-link" data-toggle="collapse" data-target="#ownership" aria-expanded="true"
                        aria-controls="ownership">
                        {{ translate('experience_cms_step.first_step') }}
                    </a>
                </h5>
            </div>
            <div id="ownership" class="accordion-body collapse in" aria-labelledby="headingOwnership"
                data-parent="#accordion">
                <div class="steps step1">
                    @include('listingCMS.templates.cardInput', [
                        'step_no' => '0',
                        'step_name' => 'ownership',
                        'input_index' => '0',
                        'step_heading' => translate('experience_cms_step.ownership_step'),
                        'image' => 'yes',
                        'url' => 'yes',
                    ])
                </div>
            </div>
        </div>
        <div class="accordion-item">
            <div class="card-header" id="headingStep1">
                <h5 class="m-0">
                    <a class="btn btn-link" data-toggle="collapse" data-target="#step1" aria-expanded="false"
                        aria-controls="step1">
                        {{ translate('experience_cms_step.listing_general_detail') }}
                    </a>
                </h5>
            </div>
            <div id="step1" class="accordion-body collapsed" aria-labelledby="step1" data-parent="#accordion">
                <div class="steps step1 divider">
                    @include('listingCMS.templates.cardInput', [
                        'step_no' => '1',
                        "step_name" => 'step-1-intro',
                        'input_index' => '1',
                        'step_heading' => translate('experience_cms_step.step_1'),
                        'image' => 'yes',
                    ])
                </div>

                <div class="steps step2 divider">
                    @include('listingCMS.templates.cardInput', [
                        'step_no' => '2',
                        "step_name" => 'type',
                        'input_index' => '2',
                        'step_heading' => translate('experience_cms_step.types_step'),
                    ])
                </div>

                <div class="steps step3 divider">
                    @include('listingCMS.templates.cardInput', [
                        'step_no' => '3',
                        "step_name" => 'map',
                        'input_index' => '3',
                        'step_heading' => translate('experience_cms_step.map_step'),
                    ])
                </div>

                <div class="steps step4 divider">
                    @include('listingCMS.templates.cardInput', [
                        'step_no' => '4',
                        "step_name" => 'address-detail',
                        'input_index' => '4',
                        'step_heading' => translate('experience_cms_step.address_step'),
                    ])
                </div>

                <div class="steps step5 divider">
                    @include('listingCMS.templates.cardInput', [
                        'step_no' => '5',
                        "step_name" => 'detail-step',
                        'input_index' => '5',
                        'step_heading' => translate('experience_cms_step.detail_step'),
                    ])
                </div>
                <div class="steps step6 divider">
                    @include('listingCMS.templates.cardInput', [
                        'step_no' => '6',
                        "step_name" => 'schedule-duration',
                        'input_index' => '6',
                        'step_heading' => translate('experience_cms_step.schedule_duration_step'),
                    ])
                </div>
                <div class="steps step7 divider">
                    @include('listingCMS.templates.cardInput', [
                        'step_no' => '7',
                        "step_name" => 'languages',
                        'input_index' => '7',
                        'step_heading' => translate('experience_cms_step.language_step'),
                    ])
                </div>
            </div>
        </div>
        <div class="accordion-item">
            <div class="card-header" id="headingThree">
                <h5 class="m-0">
                    <a class="btn btn-link collapsed" data-toggle="collapse" data-target="#step2" aria-expanded="false"
                        aria-controls="step2">
                        {{ translate('experience_cms_step.step_2_initialization') }}
                    </a>
                </h5>
            </div>
            <div id="step2" class="accordion-body collapse" aria-labelledby="headingStep2" data-parent="#accordion">
                <div class="steps step8 divider">
                    @include('listingCMS.templates.cardInput', [
                        'step_no' => '8',
                        "step_name" => 'step-2-intro',
                        'input_index' => '8',
                        'step_heading' => translate('experience_cms_step.step_2'),
                        'image' => 'yes',
                    ])
                </div>

                <div class="steps step9 divider">
                    @include('listingCMS.templates.cardInput', [
                        'step_no' => '9',
                        "step_name" => 'accessibility',
                        'input_index' => '9',
                        'step_heading' => translate('experience_cms_step.accessibility_step'),
                    ])
                </div>

                <div class="steps step10 divider">
                    @include('listingCMS.templates.cardInput', [
                        'step_no' => '10',
                        "step_name" => 'itinerary',
                        'input_index' => '10',
                        'step_heading' => translate('experience_cms_step.itinerary_step'),
                    ])
                </div>

                <div class="steps step11 divider">
                    @include('listingCMS.templates.cardInput', [
                        'step_no' => '11',
                        "step_name" => 'key-feature',
                        'input_index' => '11',
                        'step_heading' => translate('experience_cms_step.key_feature_step'),
                    ])
                </div>

                <div class="steps step12 divider">
                    @include('listingCMS.templates.cardInput', [
                        'step_no' => '12',
                        "step_name" => 'inclusion-exclusion',
                        'input_index' => '12',
                        'step_heading' => translate('experience_cms_step.inclusions_exclusions_step'),
                    ])
                </div>

                <div class="steps step13 divider">
                    @include('listingCMS.templates.cardInput', [
                        'step_no' => '13',
                        "step_name" => 'rules',
                        'input_index' => '13',
                        'step_heading' => translate('experience_cms_step.rules_step'),
                    ])
                </div>

                <div class="steps step14 divider">
                    @include('listingCMS.templates.cardInput', [
                        'step_no' => '14',
                        "step_name" => 'notes',
                        'input_index' => '14',
                        'step_heading' => translate('experience_cms_step.notes_step'),
                    ])
                </div>

                <div class="steps step15 divider">
                    @include('listingCMS.templates.cardInput', [
                        'step_no' => '15',
                        "step_name" => 'image-upload',
                        'input_index' => '15',
                        'step_heading' => translate('experience_cms_step.photos_step'),
                    ])
                </div>

                <div class="steps step16 divider">
                    @include('listingCMS.templates.cardInput', [
                        'step_no' => '16',
                        "step_name" => 'file-upload',
                        'input_index' => '16',
                        'step_heading' => translate('experience_cms_step.document_step'),
                        'url' => 'yes',
                    ])
                </div>

                <div class="steps step17 divider">
                    @include('listingCMS.templates.cardInput', [
                        'step_no' => '17',
                        "step_name" => 'title',
                        'input_index' => '17',
                        'step_heading' => translate('experience_cms_step.title_step'),
                    ])
                </div>

                <div class="steps step18 divider">
                    @include('listingCMS.templates.cardInput', [
                        'step_no' => '18',
                        "step_name" => 'description',
                        'input_index' => '18',
                        'step_heading' => translate('experience_cms_step.description_step'),
                    ])
                </div>
            </div>
        </div>
        <div class="accordion-item">
            <div class="card-header" id="headingStep3">
                <h5 class="m-0">
                    <a class="btn btn-link collapsed" data-toggle="collapse" data-target="#step3" aria-expanded="false"
                        aria-controls="step1">
                        {{ translate('experience_cms_step.finalize_listing') }}
                    </a>
                </h5>
            </div>
            <div id="step3" class="accordion-body collapse" aria-labelledby="headingStep3" data-parent="#accordion">
                <div class="steps step19 divider">
                    @include('listingCMS.templates.cardInput', [
                        'step_no' => '19',
                        "step_name" => 'step-3-intro',
                        'input_index' => '19',
                        'step_heading' => translate('experience_cms_step.step_3'),
                        'image' => 'yes',
                    ])
                </div>

                <div class="steps step20 divider">
                    @include('listingCMS.templates.cardInput', [
                        'step_no' => '20',
                        "step_name" => 'adult-price',
                        'input_index' => '20',
                        'step_heading' => translate('experience_cms_step.adult_price_step'),
                        'url' => 'yes',
                    ])
                </div>
                <div class="steps step20 divider">
                    @include('listingCMS.templates.cardInput', [
                        'step_no' => '21',
                        "step_name" => 'child-price',
                        'input_index' => '21',
                        'step_heading' => translate('experience_cms_step.child_price_step'),
                        'url' => 'yes',
                    ])
                </div>
                <div class="steps step20 divider">
                    @include('listingCMS.templates.cardInput', [
                        'step_no' => '22',
                        "step_name" => 'private-price',
                        'input_index' => '22',
                        'step_heading' => translate('experience_cms_step.private_booking_price_step'),
                        'url' => 'yes',
                    ])
                </div>

                <div class="steps step21 divider">
                    @include('listingCMS.templates.cardInput', [
                        'step_no' => '23',
                        "step_name" => 'cancellation',
                        'input_index' => '23',
                        'step_heading' => translate('experience_cms_step.cancellation_step'),
                    ])
                </div>

                <div class="steps step22 divider">
                    @include('listingCMS.templates.cardInput', [
                        'step_no' => '24',
                        "step_name" => 'availability',
                        'input_index' => '24',
                        'step_heading' => translate('experience_cms_step.book_step'),
                    ])
                </div>

                <div class="steps step23 divider">
                    @include('listingCMS.templates.cardInput', [
                        'step_no' => '25',
                        "step_name" => 'cut-off-time',
                        'input_index' => '25',
                        'step_heading' => translate('experience_cms_step.cut_off_time_step'),
                    ])
                </div>

                <div class="steps step24 divider">
                    @include('listingCMS.templates.cardInput', [
                        'step_no' => '26',
                        "step_name" => 'season-price',
                        'input_index' => '26',
                        'step_heading' => translate('experience_cms_step.seasonal_step'),
                    ])
                </div>

                <div class="steps step25 divider">
                    @include('listingCMS.templates.cardInput', [
                        'step_no' => '27',
                        "step_name" => 'discount',
                        'input_index' => '27',
                        'step_heading' => translate('experience_cms_step.discount_step'),
                    ])
                </div>
            </div>
        </div>
        <div class="accordion-item">
            <div class="card-header" id="headingLastStep">
                <h5 class="m-0">
                    <a class="btn btn-link collapsed" data-toggle="collapse" data-target="#lastStep"
                        aria-expanded="false" aria-controls="lastStep">
                        {{ translate('experience_cms_step.last_step') }}
                    </a>
                </h5>
            </div>
            <div id="lastStep" class="accordion-body collapse" aria-labelledby="headingLastStep"
                data-parent="#accordion">
                <div class="steps step26">
                    @include('listingCMS.templates.cardInput', [
                        'step_no' => '28',
                        "step_name" => 'review',
                        'input_index' => '28',
                        'step_heading' => translate('experience_cms_step.review_step_last'),
                    ])
                </div>
            </div>
        </div>
    </div>
</div>

<div class="form-group">
    <div class="col-md-4">
        <input class="btn btn_yellow" type="submit" value="{{ $submitButtonText ?? translate('content_management_system.save') }}">
    </div>
</div>
@push('js')
    <script src="{{ asset('plugins/components/dropify/dist/js/dropify.min.js') }}"></script>

    <script>
        $('.dropify').dropify();
    </script>
@endpush
