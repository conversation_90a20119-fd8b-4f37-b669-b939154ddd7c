<form action="{{ route('review_update') }}" enctype="multipart/form-data" method="POST">
    @csrf

    <div class="main_cart d-flex justify-content-between p-2 review_main_card">
        <input type="hidden" name="booking_ids" value="{{ $review->booking->ids ?? '' }}">
        <input type="hidden" name="review_id" value="{{ $review->id ?? '' }}">
        <div class="property-image">
            <img class="" src="{{ asset('website') . '/' . ($review->listing->thumbnail_image->url ?? '') }}"
                loading="lazy" width="250"
                onerror="this.onerror=null;this.src=`{{ asset('website/images/plcaeholderListingImg.png') }}`;">
        </div>
        <div class="cart-asset d-flex flex-wrap w-75 pe-3 review_wrapper">
            <div class="d-flex justify-content-between align-items-center flex-wrap w-100">
                <h4 class="fs-26 light_bold list_product_heading ">
                    {{ $review->listing->name ?? 'Listing Name Here' }}</h4>
                <p class="rating  m-0 ">
                    <i class="fa fa-star"></i>
                    {{ $review->listing->rating == 0 ? translate('user_review_popup.new_listing') : number_format($review->listing->rating, 1) }}
                </p>
            </div>
            <div class="service_provider">
                <div class="d-flex gap-2 align-items-center pt-2">
                    <div class="user_img">
                        <img class="img-fluid" src="{{ asset('website') . '/' . ($review->provider->avatar ?? '') }}"
                            onerror="this.onerror=null;this.src=`{{ asset('website/images/plcaeholderListingImg.png') }}`;"
                            alt="profile image">
                    </div>
                    <div class="provider-content">
                        <div class="provider-name">
                            {{ $review->listing->user->first_name ?? 'John' }}
                            {{ $review->listing->user->last_name ?? 'Doe' }}
                        </div>
                        <div class="provider-rating">
                            <p class="rating fs-12 text-black-50">
                                <i class="fa fa-star"></i>
                                @if (count($review->listing->user->provider_review ?? []) < 5)
                                    {{-- New Host --}}
                                    {{ translate('user_review_popup.new_host') }}
                                @else
                                    {{-- {{ count($review->listing->user->provider_review ?? []) }} {{ __('website.reviews') }} --}}
                                    {{ $providerRating ?? "5.0" }}
                                @endif
                            </p>
                        </div>
                    </div>
                </div>
                <div class="d-flex gap-5 alignitems-center pt-3 dates-wrapper">

                    @php
                        $dateLabels = [
                            1 => ['Start & End Date', translate('user_review_popup.start_date'), translate('user_review_popup.end_date')],
                            2 => ['Departure & Return Date', translate('user_review_popup.departure_date'), translate('user_review_popup.return_date')],
                            3 => ['Pick Up & Drop Off Date', translate('user_review_popup.pick_up_time'), translate('user_review_popup.drop_off_time')],
                            4 => ['Check-in & Check-out Date', translate('user_review_popup.checkin_date'), translate('user_review_popup.checkout_date')],
                        ];

                        $isSingleDay = $review->booking->detail->total_days < 1;
                        $startLabel = $isSingleDay
                            ? $dateLabels[$review->listing->category_id][1]
                            : $dateLabels[$review->listing->category_id][1];
                        $endLabel = $isSingleDay ? null : $dateLabels[$review->listing->category_id][2];
                    @endphp

                    @if ($isSingleDay)
                        <div class="start">
                            <p class="light-bold m-0 fs-16">{{ $dateLabels[$review->listing->category_id][0] }}</p>
                            <p class="fs-14 start-date">
                                {{ translateDynamic(date(config('constant.date_format'), strtotime($review->booking->check_in ?? '-'))) }}
                            </p>
                        </div>
                    @else
                        <div class="start">
                            <p class="light-bold m-0 fs-16">{{ $startLabel }}</p>
                            <p class="fs-14 start-date">
                                {{ translateDynamic(date(config('constant.date_format'), strtotime($review->booking->check_in ?? '-'))) }}
                            </p>
                        </div>
                        <div class="end">
                            <p class="light-bold m-0 fs-16">{{ $endLabel }}</p>
                            <p class="fs-14 end-date">
                                {{ translateDynamic(date(config('constant.date_format'), strtotime($review->booking->check_out ?? '-'))) }}
                            </p>
                        </div>
                    @endif

                    {{-- <div class="start">
                    <p class="light-bold m-0 fs-14">Check-in Date</p>
                    <p class="fs-14 start-date">
                        {{ date(config('constant.date_format'), strtotime($review->booking->check_in ?? '-')) }}</p>
                </div>
                <div class="end">
                    <p class="light-bold m-0 fs-14">Check-out Date</p>
                    <p class="fs-14 end-date">
                        {{ date(config('constant.date_format'), strtotime($review->booking->check_out ?? '-')) }}</p>
                </div> --}}
                </div>
            </div>
        </div>
    </div>

    <div class="rating leave_review pt-4 w-100">
        @php
            $rating_comment = array_reverse([translate('user_review_popup.poor'), translate('user_review_popup.fair'), translate('user_review_popup.good'), translate('user_review_popup.very_good'), translate('user_review_popup.excellent')]);
            $rating_numbers = array_reverse([1, 2, 3, 4, 5]);
        @endphp
        <div class="rating-options">
            <!-- Cleanliness Rating -->
            <div class="rating-option-single d-flex justify-content-between align-items-center py-1 cleanliness">
                <div class="rating_title">{{ translate('user_bookings.cleanliness') }}</div>
                <div class="rating_star_wrapper">
                    @for ($r = 0; $r < count($rating_numbers); $r++)
                        <input type="radio" class="rating-checkbox d-none" value="{{ $rating_numbers[$r] }}"
                            @if ($review->cleanliness == $rating_numbers[$r]) checked @endif name="cleanliness"
                            id="rating-clean-{{ $r }}">
                        <label for="rating-clean-{{ $r }}" tooltip="{{ $rating_comment[$r] }}">
                            {{-- <i class="fa fa-star {{ $r + 1 <= $review->cleanliness ? 'dark-yellow' : '' }}"></i> --}}
                        </label>
                    @endfor
                </div>
            </div>

            <!-- Communication Rating -->
            <div class="rating-option-single d-flex justify-content-between align-items-center py-1 communication">
                <div class="rating_title">{{ translate('user_bookings.communication') }}</div>
                <div class="rating_star_wrapper">
                    @for ($c = 0; $c < count($rating_numbers); $c++)
                        <input type="radio" class="rating-checkbox d-none" value="{{ $rating_numbers[$c] }}"
                            @if ($review->communication == $rating_numbers[$c]) checked @endif name="communication"
                            id="rating-comm-{{ $c }}">
                        <label for="rating-comm-{{ $c }}" tooltip="{{ $rating_comment[$c] }}">
                            {{-- <i class="fas fa-star {{ $c + 1 <= $review->communication ? 'dark-yellow' : '' }}"></i> --}}
                        </label>
                    @endfor
                </div>
            </div>

            <!-- Amenities/Features Rating -->
            <div class="rating-option-single d-flex justify-content-between align-items-center py-1 features">
                <div class="rating_title">{{ translate('user_bookings.amenities_features') }}</div>
                <div class="rating_star_wrapper">
                    @for ($a = 0; $a < count($rating_numbers); $a++)
                        <input type="radio" class="rating-checkbox d-none" value="{{ $rating_numbers[$a] }}"
                            @if ($review->features == $rating_numbers[$a]) checked @endif name="features"
                            id="rating-features-{{ $a }}">
                        <label for="rating-features-{{ $a }}" tooltip="{{ $rating_comment[$a] }}">
                            {{-- <i class="fas fa-star {{ $a + 1 <= $review->features ? 'dark-yellow' : '' }}"></i> --}}
                        </label>
                    @endfor
                </div>
            </div>

            <!-- Value for Money Rating -->
            <div class="rating-option-single d-flex justify-content-between align-items-center py-1 value_money">
                <div class="rating_title">{{ translate('user_bookings.value_for_money') }}</div>
                <div class="rating_star_wrapper">
                    @for ($m = 0; $m < count($rating_numbers); $m++)
                        <input type="radio" class="rating-checkbox d-none" value="{{ $rating_numbers[$m] }}"
                            @if ($review->value_of_money == $rating_numbers[$m]) checked @endif name="value_of_money"
                            id="rating-money-{{ $m }}">
                        <label for="rating-money-{{ $m }}" tooltip="{{ $rating_comment[$m] }}">
                            {{-- <i class="fas fa-star {{ $m + 1 <= $review->value_of_money ? 'dark-yellow' : '' }}"></i> --}}
                        </label>
                    @endfor
                </div>
            </div>

            <div class="rating-option-single d-flex justify-content-between align-items-center py-1 value_money">
                <div class="rating_title">{{ translate('user_bookings.accuracy_of_description') }}</div>
                <div class="rating_star_wrapper">
                    @for ($s = 0; $s < count($rating_numbers); $s++)
                        <input type="radio" class="rating-checkbox d-none"
                            @if ($review->accuracy_of_description == $rating_numbers[$s]) checked @endif value="{{ $rating_numbers[$s] }}"
                            {{-- Add a condition in above line similar to the above option replacing variable name after adding column in database @Zohaib @Ushba --}} name="accuracy_of_description"
                            id="rating-accuracy-description-{{ $s }}">
                        <label for="rating-accuracy-description-{{ $s }}"
                            tooltip="{{ $rating_comment[$s] }}">
                            <i class="bi bi-star-fill"></i>
                        </label>
                    @endfor
                </div>
            </div>

            <!-- Overall Experience Rating -->
            <div class="rating-option-single d-flex justify-content-between align-items-center py-1 overall_exp">
                <div class="bold rating_title">{{ translate('user_bookings.overall_experience') }}</div>
                <div class="rating_star_wrapper">
                    @for ($o = 0; $o < count($rating_numbers); $o++)
                        <input type="radio" class="rating-checkbox d-none" value="{{ $rating_numbers[$o] }}"
                            @if ($review->rating == $rating_numbers[$o]) checked @endif name="rating"
                            id="rating-overall-{{ $o }}">
                        <label for="rating-overall-{{ $o }}" tooltip="{{ $rating_comment[$o] }}">
                            {{-- <i class="fas fa-star {{ $o + 1 <= $review->rating ? 'dark-yellow' : '' }}"></i> --}}
                        </label>
                    @endfor
                </div>
            </div>
        </div>


        <div class="images_wrapper">
            <div class="drag_drop_photos_wrapper scrollable-section">
                @if ($review->images)
                    @foreach ($review->images as $key => $image)
                        {{-- <div class="image_wrapper">
                        <img src="{{ asset('website') . '/' . $image->image }}" width="100" height="100" alt="review image">
                    </div> --}}
                        <div class="drag_drop_photo_single sortable-element">
                            <img alt="Preview {{ $key }}" loading="lazy"
                                src="{{ asset('website') . '/' . $image->image }}">
                            <div class="delete_btn_wrapper">
                                <a class="delete_btn" href="#" data-review-id="{{ $image->id }}">
                                    <i class="fa fa-trash" aria-hidden="true"></i>
                                </a>
                            </div>
                        </div>
                    @endforeach
                @endif

                <div class="drag_drop_photo_single add_photo_box">
                    {{-- <div class="photo_icon">
                    <img src="{{ asset('website') }}/images/digital_camera.png" alt="">
                </div> --}}
                    <div class="add_photo_btn">
                        <label class="add_photos_lbl" for="add_photo_file">{{ translate('user_bookings.add_photos') }}</label>
                        <label class="plus_icon_lbl" for="add_photo_file"><i class="fa fa-plus"
                                aria-hidden="true"></i></label>
                        <input id="add_photo_file" class="no_validate" type="file" name="images[]" multiple
                            accept="image/*">
                    </div>
                </div>
            </div>
        </div>
        <hr>

        {{-- <div class="review_comment d-flex justify-content-between gap-3 align-items-center">
        <input type="hidden" name="booking_id" id="booking_id_inp" value="">
        <input type="text" name="comment" class="chat-input mt-2" placeholder="Write your review here..."
            value="{{ $review->comment ?? '' }}">
        <button class="button mt-2" type="submit">Update</button>
    </div> --}}

        <div class="review_comment">
            <input type="hidden" name="booking_id" id="booking_id_inp" value="">
            {{-- <input type="text" name="comment" class="chat-input mt-2" placeholder="Write your review here..." value="{{ $review->comment ?? '' }}"> --}}
            <div class="comment_wrapper">
                <textarea class="ck_editor no_validate" id="editor_description_{{ $review->id ?? '' }}"
                    placeholder="{{ translate('user_review_popup.write_your_review') }}" name="comment">{!! $review->comment ?? '' !!}</textarea>
                <p id="char-counter" class="text-end mt-2">0/2000</p>
            </div>
            <button class="button btn-block" type="submit">{{ translate('user_bookings.update_review') }}</button>
        </div>

        {{-- <div class="review_comment ">
        <h6>Comment</h6>
        {{ $review->comment}}
    </div> --}}
    </div>
</form>
@isset($review?->reply)
    @if (
        (auth()->user()->hasRole('service') &&
            ($review?->reply->is_updated ?? null) != 1 &&
            (!isset($review->reply->created_at) || $review->reply->created_at->diffInHours(now()) < 24)) ||
            auth()->user()->hasRole('user'))
        <form action="{{ route('review.reply', $review->id) }}" method="POST" class="reply_form">
            @csrf
            <div class="review_comment mb-3">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h6>Service Provider Reply</h6>
                    @if (auth()->user()->hasRole('user'))
                        <a class="text-danger delete_reply"
                            href="{{ route('delete_review_reply', $review->reply->id) }}"><i
                                class="fas fa-trash"></i></a>
                    @endif
                </div>

                <textarea name="reply" id="" cols="30" rows="5" class="form-control"
                    placeholder="Write your response here...">{{ $review->reply->reply ?? '' }}</textarea>
            </div>
            <button type="submit" class="btn_yellow mt-3">Post</button>
        </form>
    @endif
@endisset
