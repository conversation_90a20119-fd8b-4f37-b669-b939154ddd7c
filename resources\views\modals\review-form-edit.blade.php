<div class="main_cart d-flex justify-content-between p-2 review_main_card">
    <input type="hidden" name="review_id" value="{{ $review->id }}">
    <div class="property-image">
        <img class="img-fluid" src="{{ asset('website') . '/' . ($booking->listing->thumbnail_image->url ?? '') }}"
            loading="lazy"
            onerror="this.onerror=null;this.src=`{{ asset('website/images/plcaeholderListingImg.png') }}`;">
    </div>
    <div class="cart-asset flex-wrap w-75 pe-3">
        <div class="d-flex justify-content-between flex-wrap">
            <h4 class="fs-26 light_bold list_product_heading ">
                {{ $booking->listing->name ?? 'Listing Name Here' }}</h4>
            <p class="rating  new_listing ">
                <i class="fa fa-star"></i>
                {{ $booking->listing->rating == 0 ? translate('user_review_popup.new_listing') : number_format($review->listing->rating, 1) }}
            </p>
        </div>
        <div class="service_provider">
            <div class="d-flex gap-2 alignitems-center">
                <div class="user_img">
                    <img class="img-fluid" src="{{ asset('website') . '/' . $booking->provider->avatar }}"
                        onerror="this.onerror=null;this.src=`{{ asset('website/images/plcaeholderListingImg.png') }}`;"
                        alt="profile image">
                </div>
                <div class="provider-content">
                    <div class="provider-name">
                        {{ $booking->listing->user->first_name ?? 'John' }}
                        {{ $booking->listing->user->last_name ?? 'Doe' }}
                    </div>
                    <div class="provider-rating">
                        <p class="rating fs-12 text-black-50">
                            <i class="fa fa-star"></i>
                            @if (count($booking->listing->user->provider_review ?? []) < 5)
                                {{-- New Host --}}
                                {{ translate('user_review_popup.new_host') }}
                            @else
                                {{-- {{ count($booking->listing->user->provider_review ?? []) }} {{ __('website.reviews') }} --}}
                                {{ $providerRating ?? "5.0" }}
                            @endif
                        </p>
                    </div>
                </div>
            </div>
            <div class="d-flex gap-5 alignitems-center pt-3 dates-wrapper">
                @php
                    $dateLabels = [
                        1 => ['Start & End Date', translate('user_review_popup.start_date'), translate('user_review_popup.end_date')],
                        2 => ['Departure & Return Date', translate('user_review_popup.departure_date'), translate('user_review_popup.return_date')],
                        3 => ['Pick Up & Drop Off Date', translate('user_review_popup.pick_up_time'), translate('user_review_popup.drop_off_time')],
                        4 => ['Check-in & Check-out Date', translate('user_review_popup.checkin_date'), translate('user_review_popup.checkout_date')],
                    ];
                    $listing = $booking->listing ?? null;

                    $isSingleDay = $booking->detail->total_days < 1;
                    $startLabel = $isSingleDay
                        ? $dateLabels[$listing->category_id][1]
                        : $dateLabels[$listing->category_id][1];
                    $endLabel = $isSingleDay ? null : $dateLabels[$listing->category_id][2];
                @endphp
                @if ($isSingleDay)
                    <div class="start">
                        <p class="light-bold m-0 fs-16">{{ $dateLabels[$listing->category_id][0] }}</p>
                        <p class="fs-14 start-date">
                            {{ translateDynamic(date(config('constant.date_format'), strtotime($booking->check_in ?? '-'))) }}
                        </p>
                    </div>
                @else
                    <div class="start">
                        <p class="light-bold m-0 fs-16">{{ $startLabel }}</p>
                        <p class="fs-14 start-date">
                            {{ translateDynamic(date(config('constant.date_format'), strtotime($booking->check_in ?? '-'))) }}
                        </p>
                    </div>
                    <div class="end">
                        <p class="light-bold m-0 fs-16">{{ $endLabel }}</p>
                        <p class="fs-14 end-date">
                            {{ translateDynamic(date(config('constant.date_format'), strtotime($booking->check_out ?? '-'))) }}
                        </p>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>
<div class="rating leave_review pt-4 w-100">
    @php
        $rating_comment = array_reverse([translate('user_review_popup.poor'), translate('user_review_popup.fair'), translate('user_review_popup.good'), translate('user_review_popup.very_good'), translate('user_review_popup.excellent')]);
        $rating_numbers = array_reverse([1.0, 2.0, 3.0, 4.0, 5.0]);
    @endphp
    <div class="rating-options">
        <div class="rating-option-sinlge d-flex justify-content-between align-items-center py-2 cleanliness">
            <div>{{ translate('user_bookings.cleanliness') }}</div>
            <div class="rating_star_wrapper">
                @for ($r = 0; $r < count($rating_numbers); $r++)
                    <input type="radio" class="rating-checkbox d-none" value="{{ $rating_numbers[$r] }}"
                        name="cleanliness" id="rating-clean-1-{{ $r }}"
                        {{ $review->cleanliness == $rating_numbers[$r] ? 'checked' : '' }}>
                    <label for="rating-clean-1-{{ $r }}" tooltip="{{ $rating_comment[$r] }}">
                        <i class="bi bi-star-fill"></i>
                    </label>
                @endfor
            </div>
        </div>
        <div class="rating-option-sinlge d-flex justify-content-between align-items-center py-2 communication">
            <div> {{ translate('user_bookings.communication') }}</div>
            <div class="rating_star_wrapper">
                @for ($c = 0; $c < count($rating_numbers); $c++)
                    <input type="radio" class="rating-checkbox d-none" value="{{ $rating_numbers[$c] }}"
                        name="communication" id="rating-comm-1-{{ $c }}"
                        {{ $review->communication == $rating_numbers[$c] ? 'checked' : '' }}>
                    <label for="rating-comm-1-{{ $c }}" tooltip="{{ $rating_comment[$c] }}">
                        <i class="bi bi-star-fill"></i>
                    </label>
                @endfor
            </div>
        </div>
        <div class="rating-option-sinlge d-flex justify-content-between align-items-center py-2 features">
            <div>{{ translate('user_bookings.amenities_features') }}</div>
            <div class="rating_star_wrapper">
                @for ($a = 0; $a < count($rating_numbers); $a++)
                    <input type="radio" class="rating-checkbox d-none" value="{{ $rating_numbers[$a] }}"
                        name="features" id="rating-features-1-{{ $a }}"
                        {{ $review->features == $rating_numbers[$a] ? 'checked' : '' }}>
                    <label for="rating-features-1-{{ $a }}" tooltip="{{ $rating_comment[$a] }}">
                        <i class="bi bi-star-fill"></i>
                    </label>
                @endfor
            </div>
        </div>
        <div class="rating-option-sinlge d-flex justify-content-between align-items-center py-2 value_money">
            <div>{{ translate('user_bookings.value_for_money') }}</div>
            <div class="rating_star_wrapper">
                @for ($m = 0; $m < count($rating_numbers); $m++)
                    <input type="radio" class="rating-checkbox d-none" value="{{ $rating_numbers[$m] }}"
                        name="value_of_money" id="rating-money-1-{{ $m }}"
                        {{ $review->value_of_money == $rating_numbers[$m] ? 'checked' : '' }}>
                    <label for="rating-money-1-{{ $m }}" tooltip="{{ $rating_comment[$m] }}">
                        <i class="bi bi-star-fill"></i>
                    </label>
                @endfor
            </div>
        </div>
        <div class="rating-option-sinlge d-flex justify-content-between align-items-center py-2 value_money">
            <div>{{ translate('user_bookings.accuracy_of_description') }}</div>
            <div class="rating_star_wrapper">
                @for ($s = 0; $s < count($rating_numbers); $s++)
                    <input type="radio" class="rating-checkbox d-none" value="{{ $rating_numbers[$s] }}"
                        name="accuracy_of_description" id="rating-accuracy-description-1-{{ $s }}"
                        {{ $review->accuracy_of_description == $rating_numbers[$s] ? 'checked' : '' }}>
                    <label for="rating-accuracy-description-1-{{ $s }}"
                        tooltip="{{ $rating_comment[$s] }}">
                        <i class="bi bi-star-fill"></i>
                    </label>
                @endfor
            </div>
        </div>
        <div class="rating-option-sinlge d-flex justify-content-between align-items-center py-2 overall_exp">
            <div class="bold">{{ translate('user_bookings.overall_experience') }}</div>
            <div class="rating_star_wrapper">
                @for ($o = 0; $o < count($rating_numbers); $o++)
                    <!-- Ensure index is within bounds -->
                    <input type="radio" class="rating-checkbox d-none" value="{{ $rating_numbers[$o] }}"
                        name="rating" id="rating-overall-1-{{ $o }}"
                        {{ $review->rating == $rating_numbers[$o] ? 'checked' : '' }}>
                    <label for="rating-overall-1-{{ $o }}" tooltip="{{ $rating_comment[$o] }}">
                        <i class="bi bi-star-fill"></i>
                    </label>
                @endfor
            </div>
        </div>
    </div>
    <div class="images_wrapper my-3">
        {{-- <x-image-uploader categoryName="Review" listingId="{{ $listing->id ?? null }}" /> --}}
        {{-- <input type="file" name="images[]" id="review_images" class="d-none" multiple accept="image/*">
        <label for="review_images" class="button">Upload Images</label> --}}
        <div class="drag_drop_photos_wrapper scrollable-section">
            @foreach ($review->images as $review_image)
                <div class="drag_drop_photo_single sortable-element" data-image-id ="{{ $review_image->id }}">
                    <img src="{{ asset('website') . '/' . $review_image->image }}" alt="">
                    <div class="delete_btn_wrapper">
                        <a class="delete_btn" href="#"><i class="fa fa-trash" aria-hidden="true"></i></a>
                    </div>
                </div>
            @endforeach
            <div class="drag_drop_photo_single add_photo_box">
                <div class="add_photo_btn">
                    <label class="add_photos_lbl" for="add_photo_file">{{ translate('user_bookings.add_photos') }}</label>
                    <label class="plus_icon_lbl" for="add_photo_file"><i class="fa fa-plus"
                            aria-hidden="true"></i></label>
                    <input id="add_photo_file" class="no_validate" type="file" name="images[]" multiple
                        accept="image/*">
                </div>
            </div>
        </div>
    </div>
    <div class="review_comment">
        <div class="comment_wrapper">
            <textarea class="ck_editor no_validate" id="editor_description_{{ $booking->id }}"
                placeholder="{{ translate('user_review_popup.write_your_review') }}" name="comment">{!! $review->comment !!}</textarea>
            <p id="char-counter" class="text-end mt-2">{{ strlen(strip_tags($review->comment)) }}/2000</p>
        </div>
        @if ($review->is_updated == 0 && !isset($review->reply?->reply))
            <button class="button btn-block" type="submit">{{ translate('user_bookings.update_review') }}</button>
        @endif
    </div>
    @isset($review->reply->reply)
        <div class="review_comment">
            <h6>
                {{ translate('user_bookings.service_provider_response') }}:
            </h6>
            <p>{{ $review->reply->reply }}</p>
        </div>
    @endisset
</div>

<script>
    setTimeout(() => {
        if (typeof editorInstanceDescription_{{ $booking->id }} === 'undefined') {
            let editorInstanceDescription_{{ $booking->id }};

            ClassicEditor
                .create(document.querySelector(`#editor_description_{{ $booking->id }}`), {
                    enterMode: 'paragraph',
                    shiftEnterMode: 'softBreak',
                    toolbar: false,
                })
                .then(editor => {
                    console.log('Editor was initialized', editor);
                    editorInstanceDescription_{{ $booking->id }} = editor;

                    const maxLength = 2000;

                    function getPlainText() {
                        const editorData = editorInstanceDescription_{{ $booking->id }}.getData().trim();
                        const tempDiv = document.createElement("div");
                        tempDiv.innerHTML = editorData;
                        return tempDiv.textContent || tempDiv.innerText;
                    }

                    function toggleNextButtonState() {
                        const plainText = getPlainText();
                        const remainingChars = plainText.length;
                        window.commentLength = remainingChars;

                        $('#char-counter').text(remainingChars + '/' + maxLength);
                        $('#char-counter').css('color', remainingChars > maxLength ? 'red' : '#949494');

                        if(remainingChars < 30 && remainingChars >= 1){
                            $('#char-counter').css('color', 'red');
                        }else{
                            $('#char-counter').css('color', '#949494');
                        }

                        // Only enable .next if text is present and within limit
                        const isTextValid = remainingChars > 29 && remainingChars <= maxLength;
                        $(editorInstanceDescription_{{ $booking->id }}.ui.view.element)
                            .closest('.review_comment')
                            .find('button[type="submit"]')
                            .prop('disabled', !isTextValid);
                        window.validateRatings();
                    }

                    // Prevent input when max length is reached
                    editorInstanceDescription_{{ $booking->id }}.editing.view.document.on('beforeinput', (
                        event, data) => {
                        const editorData = editorInstanceDescription_{{ $booking->id }}.getData();
                        $(`#editor_description_{{ $booking->id }}`).val(editorData);
                        const plainText = getPlainText();
                        if (plainText.length >= maxLength && data.inputType.startsWith("insert")) {
                            event.stop(); // Stop new input
                        }
                    });

                    editorInstanceDescription_{{ $booking->id }}.editing.view.document.on('paste', (event,
                        data) => {
                        setTimeout(() => {
                            const editorData =
                                editorInstanceDescription_{{ $booking->id }}.getData();
                            $(`#editor_description_{{ $booking->id }}`).val(editorData);
                        }, 0);
                    });

                    // Monitor changes and enforce character limit
                    editorInstanceDescription_{{ $booking->id }}.model.document.on('change:data', () => {
                        const plainText = getPlainText();
                        if (plainText.length > maxLength) {
                            editorInstanceDescription_{{ $booking->id }}.execute(
                                'undo'); // Revert last input
                        }
                        toggleNextButtonState(); // Properly update button state
                    });

                    toggleNextButtonState();
                })
                .catch(error => {
                    console.error('There was a problem initializing the editor.', error);
                });
        } else {
            console.log('CKEditor is already initialized.');
        }
    }, 500);
</script>
